from django.db import models

class UserConceptMapping(models.Model):
    id = models.BigAutoField(primary_key=True)  # Auto increment BIGINT
    user_id = models.IntegerField()             # USER_ID as int
    concept_nm = models.CharField(max_length=255)  # CONCEPT_NM as varchar(255)

    class Meta:
        db_table = "user_concept_mapping"

from django.db import models


class Role(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=32, null=True, blank=True)

    class Meta:
        db_table = "role"


class Users(models.Model):
    id = models.AutoField(primary_key=True)
    first_name = models.CharField(max_length=64, null=True, blank=True)
    last_name = models.CharField(max_length=64, null=True, blank=True)
    email = models.CharField(max_length=64, null=True, blank=True)
    role = models.ForeignKey(
        Role, on_delete=models.SET_NULL, null=True, blank=True, db_column="role_id"
    )
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "users"

class ApiRequestLog(models.Model):
    timestamp = models.DateTimeField(auto_now_add=True)
    method = models.CharField(max_length=10)
    endpoint = models.CharField(max_length=255)
    user_id = models.IntegerField(null=True)  # Foreign key to Users table if user is logged in, otherwise null
    first_name = models.CharField(max_length=255, null=True)
    last_name = models.CharField(max_length=255, null=True)
    email = models.CharField(max_length=255, null=True)
    status_code = models.IntegerField()

    def __str__(self):
        return f"{self.timestamp} | {self.method} {self.endpoint} | {self.user_id} | {self.first_name} {self.last_name}"